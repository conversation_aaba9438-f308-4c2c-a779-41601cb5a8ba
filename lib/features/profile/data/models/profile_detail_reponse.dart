import 'dart:convert';

class ProfileDetailResponse {
  final String? message;
  final String? status;
  final Data? data;

  ProfileDetailResponse({
    this.message,
    this.status,
    this.data,
  });

  ProfileDetailResponse copyWith({
    String? message,
    String? status,
    Data? data,
  }) =>
      ProfileDetailResponse(
        message: message ?? this.message,
        status: status ?? this.status,
        data: data ?? this.data,
      );

  factory ProfileDetailResponse.fromRawJson(String str) =>
      ProfileDetailResponse.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory ProfileDetailResponse.fromJson(Map<String, dynamic> json) =>
      ProfileDetailResponse(
        message: json["message"],
        status: json["status"],
        data: json["data"] == null ? null : Data.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "message": message,
        "status": status,
        "data": data?.toJson(),
      };
}

class Data {
  final String? id;
  final String? userId;
  final String? firstName;
  final dynamic lastName;
  final dynamic mobile;
  final String? email;
  final dynamic yearOfBirth;
  final String? countryCode;
  final dynamic postcode;
  final String? gender;
  final String? age;
  final List<String>? identityGroup;
  final String? otherGender;
  final String? otherDemographics;
  final String? goal;

  Data({
    this.otherGender,
    this.otherDemographics,
    this.goal,
    this.id,
    this.userId,
    this.firstName,
    this.lastName,
    this.mobile,
    this.email,
    this.yearOfBirth,
    this.countryCode,
    this.postcode,
    this.gender,
    this.age,
    this.identityGroup,
  });

  Data copyWith(
          {String? id,
          String? userId,
          String? firstName,
          dynamic lastName,
          dynamic mobile,
          String? email,
          dynamic yearOfBirth,
          String? countryCode,
          dynamic postcode,
          String? gender,
          String? age,
          List<String>? identityGroup,
          final String? otherGender,
          final String? otherDemographics,
          final String? goal}) =>
      Data(
        id: id ?? this.id,
        userId: userId ?? this.userId,
        firstName: firstName ?? this.firstName,
        lastName: lastName ?? this.lastName,
        mobile: mobile ?? this.mobile,
        email: email ?? this.email,
        yearOfBirth: yearOfBirth ?? this.yearOfBirth,
        countryCode: countryCode ?? this.countryCode,
        postcode: postcode ?? this.postcode,
        gender: gender ?? this.gender,
        age: age ?? this.age,
        identityGroup: identityGroup ?? this.identityGroup,
        otherGender: otherGender ?? this.otherGender,
        otherDemographics: otherDemographics ?? this.otherDemographics,
        goal: goal ?? this.goal,
      );

  factory Data.fromRawJson(String str) => Data.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        id: json["id"],
        userId: json["user_id"],
        firstName: json["first_name"],
        lastName: json["last_name"],
        mobile: json["mobile"],
        email: json["email"],
        yearOfBirth: json["year_of_birth"],
        countryCode: json["country_code"],
        postcode: json["postcode"],
        gender: json["gender"],
        age: json["age"],
        identityGroup: json["identity_group"] == null
            ? []
            : List<String>.from(json["identity_group"]!.map((x) => x)),
        otherGender: json["other_gender"],
        otherDemographics: json["other_demographics"],
        goal: json["goal"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "user_id": userId,
        "first_name": firstName,
        "last_name": lastName,
        "mobile": mobile,
        "email": email,
        "year_of_birth": yearOfBirth,
        "country_code": countryCode,
        "postcode": postcode,
        "gender": gender,
        "age": age,
        "identity_group": identityGroup == null
            ? []
            : List<dynamic>.from(identityGroup!.map((x) => x)),
        "other_gender": otherGender,
        "other_demographics": otherDemographics,
        "goal": goal,
      };
}
