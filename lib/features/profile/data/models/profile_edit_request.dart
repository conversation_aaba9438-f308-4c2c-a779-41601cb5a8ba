class UpdateProfileParams {
  final String? firstName;
  final String? lastName;
  final String? gender;
  final String? age;
  final List<String?> identityGroup;
  final String? otherGender;
  final String? otherDemographics;
  final String? goal;

  UpdateProfileParams({
    this.otherGender,
    this.otherDemographics,
    this.goal,
    this.firstName,
    this.lastName,
    this.gender,
    this.age,
    required this.identityGroup,
  });

  Map<String, dynamic> toJson() {
    return {
      'first_name': firstName,
      'last_name': lastName,
      'gender': gender,
      'age': age,
      'identity_group': identityGroup,
      "other_gender": otherGender,
      "other_demographics": otherDemographics,
      "goal": goal,
    };
  }
}
