import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gap/gap.dart';
import 'package:gotcha_mfg_app/config/router/app_router.gr.dart';
import 'package:gotcha_mfg_app/config/theme/app_colors.dart';
import 'package:gotcha_mfg_app/core/extensions/app_text_theme_extension.dart';
import 'package:gotcha_mfg_app/core/storage/storage_service.dart';
import 'package:gotcha_mfg_app/shared/widgets/app_header.dart';
import 'package:gotcha_mfg_app/shared/widgets/primary_button.dart';
import 'package:gotcha_mfg_app/core/utils/platform_utils.dart';
import 'package:gotcha_mfg_app/core/mixpanel_service.dart';
import 'package:gotcha_mfg_app/locator.dart';

@RoutePage()
class OnboardingGoalPage extends StatefulWidget {
  const OnboardingGoalPage({super.key});

  @override
  State<OnboardingGoalPage> createState() => _OnboardingGoalPageState();
}

class _OnboardingGoalPageState extends State<OnboardingGoalPage> {
  String? selectedGoal;
  
  final List<String> goalOptions = [
    'Feel more in control of my emotions',
    'Build better relationships',
    'Handle stress better',
    'Improve my mental wellbeing',
    'Develop better coping strategies',
  ];

  @override
  void initState() {
    super.initState();
    sl<MixpanelService>().trackScreenView(
      'Onboarding Goal Page',
      properties: {'Code': 'screen_view.onboarding_goal_page'},
    );
  }

  void _selectGoal(String goal) {
    setState(() {
      selectedGoal = goal;
    });
  }

  Future<void> _saveGoalAndContinue() async {
    if (selectedGoal != null) {
      // Save goal to local storage
      await sl<StorageService>().writeData('user_goal', selectedGoal!);
      
      sl<MixpanelService>().trackButtonClick('Next', properties: {
        'Page': 'Onboarding Goal Page',
        'Selected_Goal': selectedGoal,
        'Code': 'click.onboarding_goal_page.next'
      });

      context.replaceRoute(const OnboardingFeelingRoute());
    }
  }

  Widget _buildGoalChip(String goal) {
    final isSelected = selectedGoal == goal;
    
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: ActionChip(
        shape: StadiumBorder(
          side: BorderSide(
            width: 1.5,
            color: isSelected ? AppColors.coral : Colors.white,
          ),
        ),
        onPressed: () => _selectGoal(goal),
        label: Text(
          goal,
          style: Theme.of(context).textTheme.ralewaySemiBold.copyWith(
                fontSize: 14,
                color: AppColors.navy,
              ),
        ),
        backgroundColor: isSelected ? AppColors.lightRed : Colors.white,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final size = MediaQuery.sizeOf(context);

    return Scaffold(
      appBar: AppBar(
        toolbarHeight: 0,
        elevation: 0,
        systemOverlayStyle: const SystemUiOverlayStyle(
          statusBarColor: Colors.white,
          systemNavigationBarIconBrightness: Brightness.dark,
          statusBarBrightness: Brightness.light,
          statusBarIconBrightness: Brightness.dark,
          systemNavigationBarColor: AppColors.grey,
        ),
      ),
      resizeToAvoidBottomInset: true,
      body: Padding(
        padding: EdgeInsets.only(
          top: isIos ? 4 : 8,
          left: 8,
          right: 8,
        ),
        child: Column(
          children: [
            const AppHeader(
              title: 'Getting started',
              currentStep: 0,
              totalSteps: 1,
            ),
            Expanded(
              child: Container(
                color: AppColors.navy,
                child: Container(
                  padding: EdgeInsets.only(bottom: isIos ? 80 : 56),
                  decoration: const BoxDecoration(
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(30),
                      topRight: Radius.circular(30),
                    ),
                    color: AppColors.grey,
                  ),
                  child: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Gap(12),
                        Padding(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 24,
                            vertical: 16,
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Let\'s personalize your experience',
                                style: textTheme.ralewayRegular
                                    .copyWith(fontSize: 14),
                              ),
                              const Gap(4),
                              Text(
                                'What\'s your main goal for using this app?',
                                style: textTheme.ralewaySemiBold.copyWith(
                                  fontSize: 17,
                                ),
                              ),
                            ],
                          ),
                        ),
                        const Gap(16),
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 24),
                          child: Wrap(
                            spacing: 15,
                            runSpacing: 8,
                            children: goalOptions.map((goal) => _buildGoalChip(goal)).toList(),
                          ),
                        ),
                        const Gap(120),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
      floatingActionButton: selectedGoal != null
          ? Padding(
              padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 24),
              child: SizedBox(
                width: size.width,
                child: PrimaryButton(
                  text: 'Next',
                  isEnabled: true,
                  onPressed: _saveGoalAndContinue,
                ),
              ),
            )
          : const SizedBox(),
    );
  }
}
